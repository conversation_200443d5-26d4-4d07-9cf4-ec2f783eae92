/* 全局样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    overflow-x: hidden;
    position: relative;
}

/* 背景动画 */
.background-animation {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    overflow: hidden;
}

.floating-shapes {
    position: absolute;
    width: 100%;
    height: 100%;
}

.shape {
    position: absolute;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    animation: float 6s ease-in-out infinite;
}

.shape-1 {
    width: 80px;
    height: 80px;
    top: 10%;
    left: 10%;
    animation-delay: 0s;
}

.shape-2 {
    width: 120px;
    height: 120px;
    top: 20%;
    right: 10%;
    animation-delay: 1s;
}

.shape-3 {
    width: 60px;
    height: 60px;
    bottom: 20%;
    left: 20%;
    animation-delay: 2s;
}

.shape-4 {
    width: 100px;
    height: 100px;
    bottom: 10%;
    right: 20%;
    animation-delay: 3s;
}

.shape-5 {
    width: 40px;
    height: 40px;
    top: 50%;
    left: 50%;
    animation-delay: 4s;
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
        opacity: 0.7;
    }
    50% {
        transform: translateY(-20px) rotate(180deg);
        opacity: 1;
    }
}

/* 主容器 */
.container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    padding: 20px;
}

/* 注册卡片 */
.register-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    padding: 40px;
    width: 100%;
    max-width: 450px;
    animation: slideInUp 0.8s ease-out;
    position: relative;
    overflow: hidden;
}

.register-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #667eea, #764ba2, #f093fb, #f5576c);
    background-size: 300% 100%;
    animation: gradientShift 3s ease infinite;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes gradientShift {
    0%, 100% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
}

/* 卡片头部 */
.card-header {
    text-align: center;
    margin-bottom: 30px;
}

.logo-container {
    margin-bottom: 20px;
    animation: logoFloat 3s ease-in-out infinite;
}

.logo {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    transition: transform 0.3s ease;
}

.logo:hover {
    transform: scale(1.1) rotate(5deg);
}

@keyframes logoFloat {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-10px);
    }
}

.title {
    font-size: 28px;
    font-weight: 700;
    color: #333;
    margin-bottom: 8px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.subtitle {
    color: #666;
    font-size: 14px;
    font-weight: 400;
}

/* 表单样式 */
.register-form {
    display: flex;
    flex-direction: column;
    gap: 25px;
}

.form-group {
    position: relative;
}

.input-container {
    position: relative;
    display: flex;
    align-items: center;
}

.input-icon {
    position: absolute;
    left: 15px;
    color: #999;
    font-size: 16px;
    z-index: 2;
    transition: color 0.3s ease;
}

input {
    width: 100%;
    padding: 15px 15px 15px 45px;
    border: 2px solid #e1e5e9;
    border-radius: 12px;
    font-size: 16px;
    background: #fff;
    transition: all 0.3s ease;
    outline: none;
    position: relative;
    z-index: 1;
}

input:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    transform: translateY(-2px);
}

input:focus + .floating-label,
input:not(:placeholder-shown) + .floating-label {
    transform: translateY(-25px) scale(0.85);
    color: #667eea;
    background: #fff;
    padding: 0 8px;
}

input:focus ~ .input-icon {
    color: #667eea;
    transform: scale(1.1);
}

.floating-label {
    position: absolute;
    left: 45px;
    top: 50%;
    transform: translateY(-50%);
    color: #999;
    font-size: 16px;
    pointer-events: none;
    transition: all 0.3s ease;
    background: transparent;
    z-index: 2;
}

.input-border {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background: linear-gradient(90deg, #667eea, #764ba2);
    transition: width 0.3s ease;
    border-radius: 1px;
}

input:focus ~ .input-border {
    width: 100%;
}

/* 密码切换按钮 */
.password-toggle {
    position: absolute;
    right: 15px;
    background: none;
    border: none;
    color: #999;
    cursor: pointer;
    font-size: 16px;
    transition: all 0.3s ease;
    z-index: 2;
}

.password-toggle:hover {
    color: #667eea;
    transform: scale(1.1);
}

/* 密码强度指示器 */
.password-strength {
    margin-top: 8px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.strength-bar {
    flex: 1;
    height: 4px;
    background: #e1e5e9;
    border-radius: 2px;
    overflow: hidden;
}

.strength-fill {
    height: 100%;
    width: 0%;
    border-radius: 2px;
    transition: all 0.3s ease;
    background: #e74c3c;
}

.strength-fill.weak {
    width: 33%;
    background: #e74c3c;
}

.strength-fill.medium {
    width: 66%;
    background: #f39c12;
}

.strength-fill.strong {
    width: 100%;
    background: #27ae60;
}

.strength-text {
    font-size: 12px;
    color: #666;
    min-width: 60px;
}

/* 验证消息 */
.validation-message {
    margin-top: 5px;
    font-size: 12px;
    min-height: 16px;
    transition: all 0.3s ease;
    opacity: 0;
    transform: translateY(-5px);
}

.validation-message.show {
    opacity: 1;
    transform: translateY(0);
}

.validation-message.success {
    color: #27ae60;
}

.validation-message.error {
    color: #e74c3c;
}

/* 复选框样式 */
.checkbox-group {
    margin: 10px 0;
}

.checkbox-container {
    display: flex;
    align-items: center;
    cursor: pointer;
    font-size: 14px;
    color: #666;
    position: relative;
    padding-left: 30px;
}

.checkbox-container input {
    position: absolute;
    opacity: 0;
    cursor: pointer;
    width: 0;
    height: 0;
}

.checkmark {
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    height: 20px;
    width: 20px;
    background: #fff;
    border: 2px solid #e1e5e9;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.checkbox-container:hover .checkmark {
    border-color: #667eea;
    transform: translateY(-50%) scale(1.05);
}

.checkbox-container input:checked ~ .checkmark {
    background: #667eea;
    border-color: #667eea;
}

.checkmark::after {
    content: "";
    position: absolute;
    display: none;
    left: 6px;
    top: 2px;
    width: 6px;
    height: 10px;
    border: solid white;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
}

.checkbox-container input:checked ~ .checkmark::after {
    display: block;
    animation: checkmark 0.3s ease;
}

@keyframes checkmark {
    0% {
        opacity: 0;
        transform: rotate(45deg) scale(0);
    }
    100% {
        opacity: 1;
        transform: rotate(45deg) scale(1);
    }
}

.terms-link, .privacy-link {
    color: #667eea;
    text-decoration: none;
    transition: color 0.3s ease;
}

.terms-link:hover, .privacy-link:hover {
    color: #764ba2;
    text-decoration: underline;
}

/* 注册按钮 */
.register-btn {
    width: 100%;
    padding: 15px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 12px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
    margin-top: 10px;
}

.register-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
}

.register-btn:active {
    transform: translateY(0);
}

.register-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.register-btn:hover::before {
    left: 100%;
}

.btn-text, .btn-loader, .btn-success {
    transition: all 0.3s ease;
}

.btn-loader {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    opacity: 0;
}

.btn-success {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    opacity: 0;
    font-size: 20px;
}

.register-btn.loading .btn-text {
    opacity: 0;
}

.register-btn.loading .btn-loader {
    opacity: 1;
}

.register-btn.success .btn-text,
.register-btn.success .btn-loader {
    opacity: 0;
}

.register-btn.success .btn-success {
    opacity: 1;
    animation: successPulse 0.6s ease;
}

@keyframes successPulse {
    0% {
        transform: translate(-50%, -50%) scale(0);
    }
    50% {
        transform: translate(-50%, -50%) scale(1.2);
    }
    100% {
        transform: translate(-50%, -50%) scale(1);
    }
}

/* 加载动画 */
.spinner {
    width: 20px;
    height: 20px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 卡片底部 */
.card-footer {
    text-align: center;
    margin-top: 25px;
    padding-top: 20px;
    border-top: 1px solid #e1e5e9;
    color: #666;
    font-size: 14px;
}

.login-link {
    color: #667eea;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
}

.login-link:hover {
    color: #764ba2;
    text-decoration: underline;
}

/* 模态框样式 */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
    animation: fadeIn 0.3s ease;
}

.modal.show {
    display: flex;
    justify-content: center;
    align-items: center;
}

.modal-content {
    background: white;
    padding: 40px;
    border-radius: 20px;
    text-align: center;
    max-width: 400px;
    width: 90%;
    animation: modalSlideIn 0.4s ease;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.success-icon {
    font-size: 60px;
    color: #27ae60;
    margin-bottom: 20px;
    animation: successBounce 0.6s ease;
}

@keyframes successBounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

.modal-content h3 {
    color: #333;
    margin-bottom: 15px;
    font-size: 24px;
    font-weight: 600;
}

.modal-content p {
    color: #666;
    margin-bottom: 25px;
    line-height: 1.5;
}

.modal-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 12px 30px;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
}

.modal-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
}

/* Toast 提示 */
.toast {
    position: fixed;
    top: 20px;
    right: 20px;
    background: #e74c3c;
    color: white;
    padding: 15px 20px;
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    transform: translateX(400px);
    transition: transform 0.3s ease;
    z-index: 1001;
}

.toast.show {
    transform: translateX(0);
}

.toast-content {
    display: flex;
    align-items: center;
    gap: 10px;
}

/* 响应式设计 */
@media (max-width: 480px) {
    .container {
        padding: 10px;
    }
    
    .register-card {
        padding: 30px 20px;
    }
    
    .title {
        font-size: 24px;
    }
    
    input {
        padding: 12px 12px 12px 40px;
        font-size: 14px;
    }
    
    .floating-label {
        left: 40px;
        font-size: 14px;
    }
    
    .input-icon {
        left: 12px;
        font-size: 14px;
    }
}

/* 输入验证状态 */
.input-container.success input {
    border-color: #27ae60;
}

.input-container.success .input-icon {
    color: #27ae60;
}

.input-container.error input {
    border-color: #e74c3c;
    animation: shake 0.5s ease;
}

.input-container.error .input-icon {
    color: #e74c3c;
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}
