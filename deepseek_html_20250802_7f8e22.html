<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>柠汐认证平台 | 注册</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        body {
            background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
            overflow-x: hidden;
        }

        .container {
            position: relative;
            width: 100%;
            max-width: 1200px;
            height: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        /* 装饰元素 */
        .decoration {
            position: absolute;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            z-index: 0;
            animation: float 15s infinite ease-in-out;
        }

        .decoration:nth-child(1) {
            width: 300px;
            height: 300px;
            top: 10%;
            left: 5%;
            animation-delay: 0s;
        }

        .decoration:nth-child(2) {
            width: 200px;
            height: 200px;
            bottom: 15%;
            right: 10%;
            animation-delay: -5s;
        }

        .decoration:nth-child(3) {
            width: 150px;
            height: 150px;
            top: 50%;
            left: 20%;
            animation-delay: -10s;
        }

        .card {
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(12px);
            border-radius: 24px;
            border: 1px solid rgba(255, 255, 255, 0.18);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
            width: 100%;
            max-width: 500px;
            padding: 40px;
            z-index: 10;
            transition: transform 0.4s ease, box-shadow 0.4s ease;
            overflow: hidden;
            position: relative;
        }

        .card:hover {
            transform: translateY(-10px);
            box-shadow: 0 15px 45px rgba(0, 0, 0, 0.3);
        }

        .logo-container {
            text-align: center;
            margin-bottom: 30px;
            animation: fadeInDown 1s ease;
        }

        .logo {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            background: white;
            display: flex;
            justify-content: center;
            align-items: center;
            margin: 0 auto 15px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }

        .logo img {
            width: 80%;
            height: auto;
        }

        .logo:hover {
            transform: scale(1.05) rotate(5deg);
        }

        .title {
            color: white;
            font-size: 2.2rem;
            font-weight: 700;
            margin-bottom: 5px;
            letter-spacing: 1px;
            text-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
        }

        .subtitle {
            color: rgba(255, 255, 255, 0.8);
            font-size: 1.1rem;
            font-weight: 400;
            margin-bottom: 30px;
            text-align: center;
        }

        .form-group {
            position: relative;
            margin-bottom: 30px;
        }

        .form-input {
            width: 100%;
            padding: 18px 20px;
            font-size: 16px;
            border: none;
            border-radius: 12px;
            background: rgba(255, 255, 255, 0.85);
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
            outline: none;
        }

        .form-input:focus {
            background: white;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .form-label {
            position: absolute;
            top: 18px;
            left: 20px;
            color: #666;
            font-size: 16px;
            font-weight: 400;
            transition: all 0.3s ease;
            pointer-events: none;
        }

        .form-input:focus + .form-label,
        .form-input:not(:placeholder-shown) + .form-label {
            top: -12px;
            left: 15px;
            font-size: 13px;
            background: linear-gradient(135deg, #6a11cb, #2575fc);
            color: white;
            padding: 2px 10px;
            border-radius: 10px;
        }

        .password-toggle {
            position: absolute;
            right: 15px;
            top: 50%;
            transform: translateY(-50%);
            cursor: pointer;
            color: #666;
            transition: color 0.3s;
        }

        .password-toggle:hover {
            color: #2575fc;
        }

        .btn {
            width: 100%;
            padding: 16px;
            background: linear-gradient(135deg, #6a11cb, #2575fc);
            color: white;
            font-size: 18px;
            font-weight: 600;
            border: none;
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(37, 117, 252, 0.4);
            overflow: hidden;
            position: relative;
            letter-spacing: 1px;
        }

        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 20px rgba(37, 117, 252, 0.6);
        }

        .btn:active {
            transform: translateY(1px);
        }

        .btn::after {
            content: '';
            position: absolute;
            top: -50%;
            left: -60%;
            width: 20px;
            height: 200%;
            background: rgba(255, 255, 255, 0.3);
            transform: rotate(30deg);
            transition: all 0.6s;
        }

        .btn:hover::after {
            left: 120%;
        }

        .login-link {
            text-align: center;
            margin-top: 25px;
            color: rgba(255, 255, 255, 0.8);
            font-size: 15px;
        }

        .login-link a {
            color: white;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s;
            position: relative;
        }

        .login-link a::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 0;
            width: 0;
            height: 2px;
            background: white;
            transition: width 0.3s;
        }

        .login-link a:hover::after {
            width: 100%;
        }

        .terms {
            display: flex;
            align-items: center;
            margin: 20px 0;
            color: rgba(255, 255, 255, 0.8);
            font-size: 14px;
        }

        .terms input {
            margin-right: 10px;
            width: 18px;
            height: 18px;
            cursor: pointer;
        }

        .terms a {
            color: white;
            text-decoration: none;
            font-weight: 600;
            margin-left: 5px;
        }

        /* 动画定义 */
        @keyframes float {
            0%, 100% {
                transform: translateY(0) translateX(0);
            }
            25% {
                transform: translateY(-20px) translateX(15px);
            }
            50% {
                transform: translateY(10px) translateX(-20px);
            }
            75% {
                transform: translateY(-15px) translateX(-10px);
            }
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* 响应式设计 */
        @media (max-width: 576px) {
            .card {
                padding: 30px 20px;
            }
            
            .title {
                font-size: 1.8rem;
            }
            
            .form-input {
                padding: 16px 18px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 装饰元素 -->
        <div class="decoration"></div>
        <div class="decoration"></div>
        <div class="decoration"></div>
        
        <div class="card">
            <div class="logo-container">
                <div class="logo">
                    <img src="https://d.ly-y.cn/up/diugai.com175413109649212.png" alt="柠汐认证平台">
                </div>
                <h1 class="title">柠汐认证平台</h1>
                <p class="subtitle">加入我们，开启全新体验</p>
            </div>
            
            <form id="registerForm">
                <div class="form-group">
                    <input type="text" class="form-input" id="username" placeholder=" " required>
                    <label for="username" class="form-label">用户名</label>
                </div>
                
                <div class="form-group">
                    <input type="email" class="form-input" id="email" placeholder=" " required>
                    <label for="email" class="form-label">电子邮箱</label>
                </div>
                
                <div class="form-group">
                    <input type="password" class="form-input" id="password" placeholder=" " required>
                    <label for="password" class="form-label">密码</label>
                    <span class="password-toggle" id="togglePassword">
                        <i class="far fa-eye"></i>
                    </span>
                </div>
                
                <div class="form-group">
                    <input type="password" class="form-input" id="confirmPassword" placeholder=" " required>
                    <label for="confirmPassword" class="form-label">确认密码</label>
                </div>
                
                <div class="terms">
                    <input type="checkbox" id="terms" required>
                    <label for="terms">我已阅读并同意<a href="#">服务条款</a>和<a href="#">隐私政策</a></label>
                </div>
                
                <button type="submit" class="btn">
                    <i class="fas fa-user-plus"></i> 立即注册
                </button>
            </form>
            
            <div class="login-link">
                已有账户？<a href="#">立即登录</a>
            </div>
        </div>
    </div>

    <script>
        // 密码显示/隐藏切换
        const togglePassword = document.getElementById('togglePassword');
        const password = document.getElementById('password');
        const eyeIcon = togglePassword.querySelector('i');
        
        togglePassword.addEventListener('click', function() {
            const type = password.getAttribute('type') === 'password' ? 'text' : 'password';
            password.setAttribute('type', type);
            eyeIcon.classList.toggle('fa-eye');
            eyeIcon.classList.toggle('fa-eye-slash');
        });
        
        // 表单提交动画
        const registerForm = document.getElementById('registerForm');
        
        registerForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const btn = this.querySelector('.btn');
            btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 注册中...';
            btn.style.background = 'linear-gradient(135deg, #4e0ba9, #1a60d6)';
            
            // 模拟注册过程
            setTimeout(() => {
                btn.innerHTML = '<i class="fas fa-check"></i> 注册成功!';
                btn.style.background = 'linear-gradient(135deg, #2ecc71, #27ae60)';
                
                // 重置按钮状态
                setTimeout(() => {
                    btn.innerHTML = '<i class="fas fa-user-plus"></i> 立即注册';
                    btn.style.background = 'linear-gradient(135deg, #6a11cb, #2575fc)';
                }, 2000);
            }, 1500);
        });
        
        // 输入框聚焦效果增强
        const inputs = document.querySelectorAll('.form-input');
        
        inputs.forEach(input => {
            input.addEventListener('focus', function() {
                this.parentElement.style.transform = 'scale(1.02)';
            });
            
            input.addEventListener('blur', function() {
                this.parentElement.style.transform = 'scale(1)';
            });
        });
    </script>
</body>
</html>