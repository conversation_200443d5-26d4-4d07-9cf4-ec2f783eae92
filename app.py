try:
    from flask import Flask, request, jsonify, render_template
    from flask_cors import CORS
except ImportError:
    print("Flask not installed. Installing...")
    import subprocess
    import sys
    subprocess.check_call([sys.executable, "-m", "pip", "install", "Flask", "Flask-CORS"])
    from flask import Flask, request, jsonify, render_template
    from flask_cors import CORS

import json
import os
import hashlib
import uuid
from datetime import datetime

app = Flask(__name__)
try:
    CORS(app)
except:
    pass  # 如果CORS不可用，继续运行

# 数据文件路径
DATA_FILE = 'data/users.json'

def ensure_data_directory():
    """确保数据目录存在"""
    if not os.path.exists('data'):
        os.makedirs('data')
    
    if not os.path.exists(DATA_FILE):
        with open(DATA_FILE, 'w', encoding='utf-8') as f:
            json.dump([], f, ensure_ascii=False, indent=2)

def load_users():
    """加载用户数据"""
    ensure_data_directory()
    try:
        with open(DATA_FILE, 'r', encoding='utf-8') as f:
            return json.load(f)
    except:
        return []

def save_users(users):
    """保存用户数据"""
    ensure_data_directory()
    with open(DATA_FILE, 'w', encoding='utf-8') as f:
        json.dump(users, f, ensure_ascii=False, indent=2)

def hash_password(password):
    """密码哈希"""
    return hashlib.sha256(password.encode()).hexdigest()

@app.route('/')
def index():
    """主页 - 注册页面"""
    return render_template('register.html')

@app.route('/api/register', methods=['POST'])
def register():
    """用户注册API"""
    try:
        data = request.get_json()
        
        # 验证必填字段
        required_fields = ['username', 'email', 'password']
        for field in required_fields:
            if not data.get(field):
                return jsonify({
                    'success': False,
                    'message': f'{field} 是必填字段'
                }), 400
        
        username = data['username'].strip()
        email = data['email'].strip()
        password = data['password']
        
        # 加载现有用户
        users = load_users()
        
        # 检查用户名是否已存在
        if any(user['username'] == username for user in users):
            return jsonify({
                'success': False,
                'message': '用户名已存在'
            }), 400
        
        # 检查邮箱是否已存在
        if any(user['email'] == email for user in users):
            return jsonify({
                'success': False,
                'message': '邮箱已被注册'
            }), 400
        
        # 创建新用户
        new_user = {
            'id': str(uuid.uuid4()),
            'username': username,
            'email': email,
            'password': hash_password(password),
            'created_at': datetime.now().isoformat(),
            'status': 'active'
        }
        
        users.append(new_user)
        save_users(users)
        
        return jsonify({
            'success': True,
            'message': '注册成功！',
            'user_id': new_user['id']
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'注册失败：{str(e)}'
        }), 500

@app.route('/api/check-username', methods=['POST'])
def check_username():
    """检查用户名是否可用"""
    try:
        data = request.get_json()
        username = data.get('username', '').strip()
        
        if not username:
            return jsonify({'available': False, 'message': '用户名不能为空'})
        
        users = load_users()
        available = not any(user['username'] == username for user in users)
        
        return jsonify({
            'available': available,
            'message': '用户名可用' if available else '用户名已存在'
        })
        
    except Exception as e:
        return jsonify({'available': False, 'message': str(e)})

@app.route('/api/check-email', methods=['POST'])
def check_email():
    """检查邮箱是否可用"""
    try:
        data = request.get_json()
        email = data.get('email', '').strip()
        
        if not email:
            return jsonify({'available': False, 'message': '邮箱不能为空'})
        
        users = load_users()
        available = not any(user['email'] == email for user in users)
        
        return jsonify({
            'available': available,
            'message': '邮箱可用' if available else '邮箱已被注册'
        })
        
    except Exception as e:
        return jsonify({'available': False, 'message': str(e)})

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
