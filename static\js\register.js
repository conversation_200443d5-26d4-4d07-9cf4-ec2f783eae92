// DOM元素
const registerForm = document.getElementById('registerForm');
const usernameInput = document.getElementById('username');
const emailInput = document.getElementById('email');
const passwordInput = document.getElementById('password');
const confirmPasswordInput = document.getElementById('confirmPassword');
const passwordToggle = document.getElementById('passwordToggle');
const registerBtn = document.getElementById('registerBtn');
const agreeTerms = document.getElementById('agreeTerms');

// 验证消息元素
const usernameMessage = document.getElementById('usernameMessage');
const emailMessage = document.getElementById('emailMessage');
const confirmPasswordMessage = document.getElementById('confirmPasswordMessage');

// 密码强度元素
const strengthFill = document.getElementById('strengthFill');
const strengthText = document.getElementById('strengthText');

// 模态框和提示
const successModal = document.getElementById('successModal');
const errorToast = document.getElementById('errorToast');

// 防抖函数
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// 密码可见性切换
passwordToggle.addEventListener('click', function() {
    const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
    passwordInput.setAttribute('type', type);
    
    const icon = this.querySelector('i');
    icon.classList.toggle('fa-eye');
    icon.classList.toggle('fa-eye-slash');
    
    // 添加点击动画
    this.style.transform = 'scale(0.9)';
    setTimeout(() => {
        this.style.transform = 'scale(1)';
    }, 150);
});

// 密码强度检测
function checkPasswordStrength(password) {
    let strength = 0;
    let feedback = '';
    
    if (password.length >= 8) strength++;
    if (/[a-z]/.test(password)) strength++;
    if (/[A-Z]/.test(password)) strength++;
    if (/[0-9]/.test(password)) strength++;
    if (/[^A-Za-z0-9]/.test(password)) strength++;
    
    strengthFill.className = 'strength-fill';
    
    if (strength <= 2) {
        strengthFill.classList.add('weak');
        feedback = '弱';
    } else if (strength <= 3) {
        strengthFill.classList.add('medium');
        feedback = '中等';
    } else {
        strengthFill.classList.add('strong');
        feedback = '强';
    }
    
    strengthText.textContent = password ? `密码强度: ${feedback}` : '密码强度';
}

passwordInput.addEventListener('input', function() {
    checkPasswordStrength(this.value);
});

// 用户名验证
const checkUsername = debounce(async function(username) {
    if (!username || username.length < 3) {
        showValidationMessage(usernameMessage, '用户名至少需要3个字符', 'error');
        setInputState(usernameInput, 'error');
        return;
    }
    
    try {
        const response = await fetch('/api/check-username', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ username })
        });
        
        const result = await response.json();
        
        if (result.available) {
            showValidationMessage(usernameMessage, '用户名可用', 'success');
            setInputState(usernameInput, 'success');
        } else {
            showValidationMessage(usernameMessage, result.message, 'error');
            setInputState(usernameInput, 'error');
        }
    } catch (error) {
        console.error('检查用户名失败:', error);
    }
}, 500);

usernameInput.addEventListener('input', function() {
    checkUsername(this.value.trim());
});

// 邮箱验证
const checkEmail = debounce(async function(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    
    if (!email) {
        clearValidationMessage(emailMessage);
        setInputState(emailInput, '');
        return;
    }
    
    if (!emailRegex.test(email)) {
        showValidationMessage(emailMessage, '请输入有效的邮箱地址', 'error');
        setInputState(emailInput, 'error');
        return;
    }
    
    try {
        const response = await fetch('/api/check-email', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ email })
        });
        
        const result = await response.json();
        
        if (result.available) {
            showValidationMessage(emailMessage, '邮箱可用', 'success');
            setInputState(emailInput, 'success');
        } else {
            showValidationMessage(emailMessage, result.message, 'error');
            setInputState(emailInput, 'error');
        }
    } catch (error) {
        console.error('检查邮箱失败:', error);
    }
}, 500);

emailInput.addEventListener('input', function() {
    checkEmail(this.value.trim());
});

// 确认密码验证
confirmPasswordInput.addEventListener('input', function() {
    const password = passwordInput.value;
    const confirmPassword = this.value;
    
    if (!confirmPassword) {
        clearValidationMessage(confirmPasswordMessage);
        setInputState(confirmPasswordInput, '');
        return;
    }
    
    if (password === confirmPassword) {
        showValidationMessage(confirmPasswordMessage, '密码匹配', 'success');
        setInputState(confirmPasswordInput, 'success');
    } else {
        showValidationMessage(confirmPasswordMessage, '密码不匹配', 'error');
        setInputState(confirmPasswordInput, 'error');
    }
});

// 显示验证消息
function showValidationMessage(element, message, type) {
    element.textContent = message;
    element.className = `validation-message show ${type}`;
}

// 清除验证消息
function clearValidationMessage(element) {
    element.className = 'validation-message';
    element.textContent = '';
}

// 设置输入框状态
function setInputState(input, state) {
    const container = input.closest('.input-container');
    container.className = `input-container ${state}`;
}

// 表单提交
registerForm.addEventListener('submit', async function(e) {
    e.preventDefault();
    
    // 检查所有字段是否填写
    const formData = {
        username: usernameInput.value.trim(),
        email: emailInput.value.trim(),
        password: passwordInput.value,
        confirmPassword: confirmPasswordInput.value
    };
    
    // 基本验证
    if (!formData.username || !formData.email || !formData.password || !formData.confirmPassword) {
        showToast('请填写所有必填字段', 'error');
        return;
    }
    
    if (formData.password !== formData.confirmPassword) {
        showToast('密码不匹配', 'error');
        return;
    }
    
    if (!agreeTerms.checked) {
        showToast('请同意服务条款和隐私政策', 'error');
        return;
    }
    
    // 设置按钮加载状态
    setButtonState('loading');
    
    try {
        const response = await fetch('/api/register', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                username: formData.username,
                email: formData.email,
                password: formData.password
            })
        });
        
        const result = await response.json();
        
        if (result.success) {
            setButtonState('success');
            setTimeout(() => {
                showSuccessModal();
                setButtonState('normal');
            }, 1000);
        } else {
            setButtonState('normal');
            showToast(result.message, 'error');
        }
        
    } catch (error) {
        setButtonState('normal');
        showToast('注册失败，请稍后重试', 'error');
        console.error('注册错误:', error);
    }
});

// 设置按钮状态
function setButtonState(state) {
    registerBtn.className = `register-btn ${state}`;
    registerBtn.disabled = state === 'loading';
}

// 显示成功模态框
function showSuccessModal() {
    successModal.classList.add('show');
}

// 关闭模态框
function closeModal() {
    successModal.classList.remove('show');
    // 可以在这里添加跳转到登录页面的逻辑
}

// 显示Toast提示
function showToast(message, type = 'error') {
    const toast = errorToast;
    const toastMessage = toast.querySelector('.toast-message');
    
    toastMessage.textContent = message;
    toast.style.background = type === 'error' ? '#e74c3c' : '#27ae60';
    
    toast.classList.add('show');
    
    setTimeout(() => {
        toast.classList.remove('show');
    }, 3000);
}

// 输入框焦点动画
document.querySelectorAll('input').forEach(input => {
    input.addEventListener('focus', function() {
        this.closest('.input-container').style.transform = 'translateY(-2px)';
    });
    
    input.addEventListener('blur', function() {
        this.closest('.input-container').style.transform = 'translateY(0)';
    });
});

// 页面加载动画
document.addEventListener('DOMContentLoaded', function() {
    // 延迟显示表单元素，创建渐入效果
    const formGroups = document.querySelectorAll('.form-group');
    formGroups.forEach((group, index) => {
        group.style.opacity = '0';
        group.style.transform = 'translateY(20px)';
        
        setTimeout(() => {
            group.style.transition = 'all 0.6s ease';
            group.style.opacity = '1';
            group.style.transform = 'translateY(0)';
        }, 200 + index * 100);
    });
    
    // 添加鼠标移动视差效果
    document.addEventListener('mousemove', function(e) {
        const shapes = document.querySelectorAll('.shape');
        const x = e.clientX / window.innerWidth;
        const y = e.clientY / window.innerHeight;
        
        shapes.forEach((shape, index) => {
            const speed = (index + 1) * 0.5;
            const xPos = (x - 0.5) * speed;
            const yPos = (y - 0.5) * speed;
            
            shape.style.transform = `translate(${xPos}px, ${yPos}px)`;
        });
    });
});

// 复选框动画
agreeTerms.addEventListener('change', function() {
    const container = this.closest('.checkbox-container');
    if (this.checked) {
        container.style.transform = 'scale(1.05)';
        setTimeout(() => {
            container.style.transform = 'scale(1)';
        }, 200);
    }
});
